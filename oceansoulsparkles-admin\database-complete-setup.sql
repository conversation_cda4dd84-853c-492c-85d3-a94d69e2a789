-- <PERSON> Soul Sparkles Complete Database Setup
-- This creates all necessary tables for the admin dashboard, POS, booking, and customer management

-- Ensure we can create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. ADMIN TABLES (Already created but ensuring they exist)
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHA<PERSON>(100) NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('DEV', 'Admin', 'Artist', 'Braider')),
  is_active BOOLEAN DEFAULT true,
  mfa_enabled BOOLEAN DEFAULT false,
  mfa_secret TEXT,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. CUSTOMERS TABLE
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  name VARCHAR(200), -- Combined name field for POS compatibility
  email VARCHAR(255) UNIQUE,
  phone VARCHAR(20),
  date_of_birth DATE,
  address TEXT,
  emergency_contact_name VARCHAR(200),
  emergency_contact_phone VARCHAR(20),
  notes TEXT,
  created_via VARCHAR(50) DEFAULT 'admin', -- 'admin', 'pos', 'website'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ARTIST PROFILES TABLE
CREATE TABLE IF NOT EXISTS artist_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_user_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  name VARCHAR(200) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  specializations TEXT[], -- Array of specializations
  bio TEXT,
  hourly_rate DECIMAL(10,2),
  commission_rate DECIMAL(5,2) DEFAULT 30.00, -- Percentage
  is_active BOOLEAN DEFAULT true,
  is_available_today BOOLEAN DEFAULT true,
  max_daily_bookings INTEGER DEFAULT 8,
  rating DECIMAL(3,2) DEFAULT 5.00,
  total_bookings INTEGER DEFAULT 0,
  total_revenue DECIMAL(12,2) DEFAULT 0.00,
  joined_date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. SERVICES TABLE
CREATE TABLE IF NOT EXISTS services (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100), -- 'face_painting', 'hair_braiding', 'glitter_art', etc.
  base_price DECIMAL(10,2),
  duration INTEGER, -- minutes
  is_active BOOLEAN DEFAULT true,
  requires_consultation BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. SERVICE PRICING TIERS TABLE
CREATE TABLE IF NOT EXISTS service_pricing_tiers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL, -- 'Basic', 'Standard', 'Premium', etc.
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  duration INTEGER NOT NULL, -- minutes
  is_default BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. ARTIST AVAILABILITY TABLE
CREATE TABLE IF NOT EXISTS artist_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  artist_id UUID REFERENCES artist_profiles(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL, -- 0 = Sunday, 1 = Monday, etc.
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  break_start_time TIME,
  break_end_time TIME,
  is_available BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. BOOKINGS TABLE
CREATE TABLE IF NOT EXISTS bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id),
  artist_id UUID REFERENCES artist_profiles(id),
  assigned_artist_id UUID REFERENCES artist_profiles(id),
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  booking_date DATE GENERATED ALWAYS AS (start_time::date) STORED,
  booking_time TIME GENERATED ALWAYS AS (start_time::time) STORED,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled', 'no_show')),
  location VARCHAR(200) DEFAULT 'Studio',
  total_amount DECIMAL(10,2),
  notes TEXT,
  booking_source VARCHAR(50) DEFAULT 'admin', -- 'admin', 'pos', 'website', 'phone'
  pos_session_id VARCHAR(100), -- For POS bookings
  tier_name VARCHAR(100),
  tier_price DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. PAYMENTS TABLE
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'AUD',
  method VARCHAR(50) NOT NULL, -- 'cash', 'card', 'square_terminal', 'square_online'
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  transaction_id VARCHAR(255), -- Square or other payment processor ID
  processing_fee DECIMAL(10,2) DEFAULT 0.00,
  cash_received DECIMAL(10,2), -- For cash payments
  change_amount DECIMAL(10,2), -- For cash payments
  receipt_data JSONB, -- Store receipt information
  payment_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. INVENTORY TABLE (For POS)
CREATE TABLE IF NOT EXISTS inventory (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  sku VARCHAR(100) UNIQUE,
  price DECIMAL(10,2),
  cost DECIMAL(10,2),
  quantity_on_hand INTEGER DEFAULT 0,
  min_stock_level INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  requires_artist BOOLEAN DEFAULT false, -- True for services that need artist assignment
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. USER PROFILES TABLE (For customer portal integration)
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200),
  email VARCHAR(255),
  phone VARCHAR(20),
  role VARCHAR(50) DEFAULT 'customer',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. AUDIT LOGS TABLE (already created but ensuring it exists)
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action VARCHAR(100) NOT NULL,
  user_id UUID REFERENCES admin_users(id),
  user_role VARCHAR(20),
  email VARCHAR(255),
  ip_address INET,
  path TEXT,
  resource VARCHAR(100),
  resource_id TEXT,
  old_values JSONB,
  new_values JSONB,
  reason TEXT,
  error TEXT,
  metadata JSONB,
  severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 12. SYSTEM SETTINGS TABLE
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category VARCHAR(50) NOT NULL,
  key VARCHAR(100) NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(category, key)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);

CREATE INDEX IF NOT EXISTS idx_artist_profiles_active ON artist_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_artist_profiles_available ON artist_profiles(is_available_today);

CREATE INDEX IF NOT EXISTS idx_services_category ON services(category);
CREATE INDEX IF NOT EXISTS idx_services_active ON services(is_active);

CREATE INDEX IF NOT EXISTS idx_bookings_customer_id ON bookings(customer_id);
CREATE INDEX IF NOT EXISTS idx_bookings_artist_id ON bookings(artist_id);
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(booking_date);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_start_time ON bookings(start_time);

CREATE INDEX IF NOT EXISTS idx_payments_booking_id ON payments(booking_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_method ON payments(method);

CREATE INDEX IF NOT EXISTS idx_artist_availability_artist ON artist_availability(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_availability_day ON artist_availability(day_of_week);

-- Audit logs indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity ON audit_logs(severity);

-- System settings indexes
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key);
CREATE INDEX IF NOT EXISTS idx_system_settings_category_key ON system_settings(category, key);

-- Enable Row Level Security
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE artist_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_pricing_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE artist_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies (Allow admin access to everything)
CREATE POLICY IF NOT EXISTS "Admin full access customers" ON customers FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access artist_profiles" ON artist_profiles FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access services" ON services FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access service_pricing_tiers" ON service_pricing_tiers FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access artist_availability" ON artist_availability FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access bookings" ON bookings FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access payments" ON payments FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access inventory" ON inventory FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access user_profiles" ON user_profiles FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access system_settings" ON system_settings FOR ALL USING (true);

-- Insert sample data for testing
-- Sample Services
INSERT INTO services (id, name, description, category, base_price, duration, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Basic Face Paint', 'Simple face painting designs', 'face_painting', 25.00, 30, true),
('550e8400-e29b-41d4-a716-446655440002', 'Festival Face Paint', 'Complex festival-style face painting', 'face_painting', 45.00, 60, true),
('550e8400-e29b-41d4-a716-446655440003', 'Hair Braiding - Simple', 'Basic braiding styles', 'hair_braiding', 35.00, 45, true),
('550e8400-e29b-41d4-a716-446655440004', 'Hair Braiding - Complex', 'Intricate braiding designs', 'hair_braiding', 65.00, 90, true),
('550e8400-e29b-41d4-a716-446655440005', 'Glitter Art Design', 'Glitter body art and designs', 'glitter_art', 30.00, 40, true)
ON CONFLICT (id) DO NOTHING;

-- Sample Service Pricing Tiers
INSERT INTO service_pricing_tiers (service_id, name, description, price, duration, is_default, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Quick Design', '15-minute simple design', 15.00, 15, false, 1),
('550e8400-e29b-41d4-a716-446655440001', 'Standard', '30-minute standard design', 25.00, 30, true, 2),
('550e8400-e29b-41d4-a716-446655440001', 'Detailed', '45-minute detailed design', 35.00, 45, false, 3),

('550e8400-e29b-41d4-a716-446655440002', 'Basic Festival', '45-minute festival design', 40.00, 45, false, 1),
('550e8400-e29b-41d4-a716-446655440002', 'Standard Festival', '60-minute festival design', 50.00, 60, true, 2),
('550e8400-e29b-41d4-a716-446655440002', 'Premium Festival', '90-minute elaborate design', 75.00, 90, false, 3),

('550e8400-e29b-41d4-a716-446655440003', 'Simple Style', '30-minute basic braids', 25.00, 30, false, 1),
('550e8400-e29b-41d4-a716-446655440003', 'Standard Style', '45-minute standard braids', 35.00, 45, true, 2),
('550e8400-e29b-41d4-a716-446655440003', 'Complex Style', '60-minute intricate braids', 50.00, 60, false, 3);

-- Sample Artist Profiles
INSERT INTO artist_profiles (id, name, email, phone, specializations, bio, hourly_rate, is_active, is_available_today, rating, total_bookings, total_revenue) VALUES
('650e8400-e29b-41d4-a716-446655440001', 'Emma Wilson', '<EMAIL>', '+61-400-111-222', ARRAY['face_painting', 'glitter_art'], 'Specialist in festival and event face painting', 45.00, true, true, 4.8, 156, 7800.00),
('650e8400-e29b-41d4-a716-446655440002', 'Lisa Brown', '<EMAIL>', '+61-400-333-444', ARRAY['hair_braiding', 'face_painting'], 'Expert in complex hair braiding and styling', 50.00, true, true, 4.9, 203, 12150.00),
('650e8400-e29b-41d4-a716-446655440003', 'Sophie Taylor', '<EMAIL>', '+61-400-555-666', ARRAY['glitter_art', 'body_art'], 'Creative glitter and body art specialist', 40.00, true, true, 4.7, 98, 4900.00),
('650e8400-e29b-41d4-a716-446655440004', 'Maya Chen', '<EMAIL>', '+61-400-777-888', ARRAY['face_painting', 'hair_braiding'], 'Versatile artist with multicultural techniques', 48.00, true, false, 4.6, 87, 4176.00)
ON CONFLICT (id) DO NOTHING;

-- Sample Artist Availability (Monday to Saturday, 9 AM to 5 PM)
INSERT INTO artist_availability (artist_id, day_of_week, start_time, end_time, break_start_time, break_end_time, is_available) VALUES
-- Emma Wilson (Mon-Sat)
('650e8400-e29b-41d4-a716-446655440001', 1, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 2, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 3, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 4, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 5, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 6, '10:00:00', '16:00:00', NULL, NULL, true),

-- Lisa Brown (Mon-Sat)
('650e8400-e29b-41d4-a716-446655440002', 1, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 2, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 3, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 4, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 5, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 6, '09:00:00', '15:00:00', NULL, NULL, true),

-- Sophie Taylor (Tue-Sat)
('650e8400-e29b-41d4-a716-446655440003', 2, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 3, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 4, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 5, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 6, '10:00:00', '16:00:00', NULL, NULL, true);

-- Sample Customers
INSERT INTO customers (id, first_name, last_name, name, email, phone, notes, created_via) VALUES
('750e8400-e29b-41d4-a716-446655440001', 'Sarah', 'Johnson', 'Sarah Johnson', '<EMAIL>', '+61-400-100-001', 'Regular customer, prefers Emma for face painting', 'admin'),
('750e8400-e29b-41d4-a716-446655440002', 'Mike', 'Chen', 'Mike Chen', '<EMAIL>', '+61-400-100-002', 'Event organizer, books multiple services', 'admin'),
('750e8400-e29b-41d4-a716-446655440003', 'Emma', 'Davis', 'Emma Davis', '<EMAIL>', '+61-400-100-003', 'Mother of three, regular family bookings', 'admin')
ON CONFLICT (id) DO NOTHING;

-- Sample Bookings (some in the future for testing)
INSERT INTO bookings (id, customer_id, service_id, artist_id, assigned_artist_id, start_time, end_time, status, total_amount, notes, booking_source) VALUES
('850e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', '2025-01-16 14:00:00+00', '2025-01-16 15:00:00+00', 'confirmed', 50.00, 'Birthday party face painting', 'admin'),
('850e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440002', '2025-01-17 10:00:00+00', '2025-01-17 11:30:00+00', 'confirmed', 65.00, 'Wedding party hair braiding', 'admin'),
('850e8400-e29b-41d4-a716-446655440003', '750e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440005', '650e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440003', '2025-01-18 15:30:00+00', '2025-01-18 16:10:00+00', 'pending', 30.00, 'School event glitter art', 'admin')
ON CONFLICT (id) DO NOTHING;

-- Sample Payments
INSERT INTO payments (booking_id, amount, currency, method, status, transaction_id) VALUES
('850e8400-e29b-41d4-a716-446655440001', 50.00, 'AUD', 'card', 'completed', 'sq_txn_001'),
('850e8400-e29b-41d4-a716-446655440002', 65.00, 'AUD', 'cash', 'completed', NULL)
ON CONFLICT DO NOTHING;

-- Sample System Settings
INSERT INTO system_settings (category, key, value, description) VALUES
('general', 'businessName', 'Ocean Soul Sparkles', 'Business name displayed throughout the application'),
('general', 'businessEmail', '<EMAIL>', 'Primary business email address'),
('general', 'businessPhone', '+61 XXX XXX XXX', 'Primary business phone number'),
('general', 'businessAddress', 'Australia', 'Business address'),
('general', 'timezone', 'Australia/Sydney', 'Business timezone'),
('general', 'currency', 'AUD', 'Default currency for transactions'),

('booking', 'defaultBookingDuration', '60', 'Default booking duration in minutes'),
('booking', 'advanceBookingDays', '30', 'How many days in advance customers can book'),
('booking', 'cancellationHours', '24', 'Minimum hours before booking for cancellation'),
('booking', 'autoConfirmBookings', 'true', 'Automatically confirm new bookings'),
('booking', 'requireDeposit', 'false', 'Require deposit for bookings'),
('booking', 'depositPercentage', '20', 'Deposit percentage if required'),

('payment', 'squareEnabled', 'true', 'Enable Square payment processing'),
('payment', 'squareEnvironment', 'sandbox', 'Square environment (sandbox/production)'),
('payment', 'cashEnabled', 'true', 'Accept cash payments'),
('payment', 'cardEnabled', 'true', 'Accept card payments'),
('payment', 'allowPartialPayments', 'true', 'Allow partial payments'),
('payment', 'autoProcessRefunds', 'false', 'Automatically process refunds'),

('notifications', 'emailNotifications', 'true', 'Enable email notifications'),
('notifications', 'smsNotifications', 'false', 'Enable SMS notifications'),
('notifications', 'bookingReminders', 'true', 'Send booking reminders'),
('notifications', 'reminderHours', '24', 'Hours before booking to send reminder'),
('notifications', 'adminNotifications', 'true', 'Send notifications to admin'),
('notifications', 'customerNotifications', 'true', 'Send notifications to customers'),

('security', 'sessionTimeout', '1800', 'Session timeout in seconds'),
('security', 'adminSessionTimeout', '1800', 'Admin session timeout in seconds'),
('security', 'maxLoginAttempts', '5', 'Maximum login attempts before lockout'),
('security', 'lockoutDuration', '900', 'Lockout duration in seconds'),
('security', 'requireMFA', 'false', 'Require multi-factor authentication'),
('security', 'ipRestrictions', 'false', 'Enable IP address restrictions')
ON CONFLICT (category, key) DO NOTHING;

-- Create development admin user if not exists
INSERT INTO admin_users (email, password_hash, first_name, last_name, role, is_active, mfa_enabled)
VALUES (
  '<EMAIL>',
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtGtJbPiOy', -- DevPassword123!
  'Development',
  'Admin',
  'DEV',
  true,
  false
)
ON CONFLICT (email) DO NOTHING;

-- Success message
SELECT 'Complete database setup completed successfully!' as message;
SELECT 'Sample data inserted for testing purposes.' as message;
